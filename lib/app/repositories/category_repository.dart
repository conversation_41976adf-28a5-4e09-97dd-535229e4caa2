import 'package:objectid/objectid.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../../objectbox.g.dart';
import '../models/erp_category.dart';
import '../providers/box_provider.dart';

class CategoryRepository {
  final BoxProvider _boxProvider;
  Talker get talker => _boxProvider.talker;
  Box<ErpCategory> get box => Box<ErpCategory>(_boxProvider.store);

  CategoryRepository(this._boxProvider);

  // 異步獲取所有類別
  Future<List<ErpCategory>> getAllAsync({bool includeDeleted = false}) async {
    try {
      if (includeDeleted) {
        // 返回所有類別，包括已刪除的
        return await box.getAllAsync();
      } else {
        // 僅返回未刪除的類別
        final query = box.query(ErpCategory_.deletedAt.isNull()).build();
        final results = await query.findAsync();
        query.close();
        return results;
      }
    } catch (e, s) {
      talker.error('Failed to get all categories async: $e', e, s);
      rethrow;
    }
  }

  // 異步按ID獲取類別
  Future<ErpCategory?> getByIdAsync(int id) async {
    try {
      return await box.getAsync(id);
    } catch (e, s) {
      talker.error('Failed to get category by id async: $e', e, s);
      rethrow;
    }
  }

  // 異步按 UUID 獲取類別
  Future<ErpCategory?> getByObjectIdAsync(String objectId) async {
    try {
      final query = box.query(ErpCategory_.objectId.equals(objectId)).build();
      final results = await query.findAsync();
      query.close();
      return results.isNotEmpty ? results.first : null;
    } catch (e, s) {
      talker.error('Failed to get category by objectId async: $e', e, s);
      rethrow;
    }
  }

  // 異步按名稱搜尋類別
  Future<List<ErpCategory>> searchByNameAsync(String name) async {
    try {
      final query = box
          .query(ErpCategory_.name.contains(name) &
              ErpCategory_.deletedAt.isNull())
          .build();
      final results = await query.findAsync();
      query.close();
      return results;
    } catch (e, s) {
      talker.error('Failed to search categories by name async: $e', e, s);
      rethrow;
    }
  }

  // 異步新增類別
  Future<int> addAsync(ErpCategory category) async {
    try {
      // 設置創建時間和UUID（如果未提供）
      final now = DateTime.now();
      category.createdAt ??= now;
      category.updatedAt = now;
      category.objectId ??= ObjectId().hexString;

      return await box.putAsync(category);
    } catch (e, s) {
      talker.error('Failed to add category async: $e', e, s);
      rethrow;
    }
  }

  // 異步更新類別
  Future<int> updateAsync(ErpCategory category) async {
    try {
      // 確保更新時間已設置
      category.updatedAt = DateTime.now();

      return await box.putAsync(category);
    } catch (e, s) {
      talker.error('Failed to update category async: $e', e, s);
      rethrow;
    }
  }

  // 異步批量添加或更新類別
  Future<List<int>> putManyAsync(List<ErpCategory> categories) async {
    try {
      final now = DateTime.now();
      for (final category in categories) {
        // 如果是新物件，設置創建時間和UUID
        if (category.id == null) {
          category.createdAt ??= now;
          category.objectId ??= ObjectId().hexString;
        }
        category.updatedAt = now;
      }

      return await box.putManyAsync(categories);
    } catch (e, s) {
      talker.error('Failed to put many categories async: $e', e, s);
      rethrow;
    }
  }

  // 異步軟刪除類別（設置刪除時間而不是實際刪除）
  Future<bool> softDeleteAsync(int id) async {
    try {
      final category = await box.getAsync(id);
      if (category != null) {
        category.deletedAt = DateTime.now();
        await box.putAsync(category);
        return true;
      }
      return false;
    } catch (e, s) {
      talker.error('Failed to soft delete category async: $e', e, s);
      rethrow;
    }
  }

  // 異步恢復已軟刪除的類別
  Future<bool> restoreAsync(int id) async {
    try {
      final category = await box.getAsync(id);
      if (category != null && category.deletedAt != null) {
        category.deletedAt = null;
        await box.putAsync(category);
        return true;
      }
      return false;
    } catch (e, s) {
      talker.error('Failed to restore category async: $e', e, s);
      rethrow;
    }
  }

  // 異步硬刪除類別（真正從資料庫中刪除）
  Future<bool> hardDeleteAsync(int id) async {
    try {
      return await box.removeAsync(id);
    } catch (e, s) {
      talker.error('Failed to hard delete category async: $e', e, s);
      rethrow;
    }
  }

  // 異步刪除所有類別
  Future<int> deleteAllAsync() async {
    try {
      return await box.removeAllAsync();
    } catch (e, s) {
      talker.error('Failed to delete all categories async: $e', e, s);
      rethrow;
    }
  }

  // 異步獲取類別總數
  Future<int> countAsync({bool includeDeleted = false}) async {
    try {
      if (includeDeleted) {
        // 獲取所有類別並計算數量
        final categories = await box.getAllAsync();
        return categories.length;
      } else {
        // 使用查詢來計算未刪除的類別數量
        final categories = await getAllAsync(includeDeleted: false);
        return categories.length;
      }
    } catch (e, s) {
      talker.error('Failed to count categories async: $e', e, s);
      rethrow;
    }
  }
}

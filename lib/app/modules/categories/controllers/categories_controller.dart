import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';
import '../../../models/erp_category.dart';
import '../../../repositories/category_repository.dart';
import '../../../providers/box_provider.dart';
import '../../../routes/app_pages.dart';

class CategoriesController extends GetxController with StateMixin<List<ErpCategory>> {
  late final CategoryRepository _categoryRepository;
  late final Talker _talker;

  // Observable variables
  final categories = <ErpCategory>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initializeRepository();
    _loadCategories();
  }

  void _initializeRepository() {
    final boxProvider = Get.find<BoxProvider>();
    _talker = boxProvider.talker;
    _categoryRepository = CategoryRepository(boxProvider.store, talker: _talker);
  }

  Future<void> _loadCategories() async {
    try {
      change(null, status: RxStatus.loading());

      final categoryList = await _categoryRepository.getAllSortedAsync();

      // If no categories exist, create sample data
      if (categoryList.isEmpty) {
        await _createSampleCategories();
        final updatedList = await _categoryRepository.getAllSortedAsync();
        categories.value = updatedList;
        change(updatedList, status: RxStatus.success());
      } else {
        categories.value = categoryList;
        change(categoryList, status: RxStatus.success());
      }
    } catch (e, s) {
      _talker.error('Failed to load categories: $e', e, s);
      change(null, status: RxStatus.error('Failed to load categories: $e'));
    }
  }

  Future<void> _createSampleCategories() async {
    final sampleCategories = [
      ErpCategory(
        name: '薪水',
        color: '#10B981',
        sort: 1,
      ),
      ErpCategory(
        name: '餐飲',
        color: '#EF4444',
        sort: 2,
      ),
      ErpCategory(
        name: '購物',
        color: '#8B5CF6',
        sort: 3,
      ),
      ErpCategory(
        name: '交通',
        color: '#3B82F6',
        sort: 4,
      ),
      ErpCategory(
        name: '娛樂',
        color: '#F59E0B',
        sort: 5,
      ),
      ErpCategory(
        name: '獎金',
        color: '#F59E0B',
        sort: 6,
      ),
      ErpCategory(
        name: '居家',
        color: '#6B7280',
        sort: 7,
      ),
      ErpCategory(
        name: '副業',
        color: '#7C3AED',
        sort: 8,
      ),
    ];

    await _categoryRepository.putManyAsync(sampleCategories);
  }

  void navigateToCategoryDetail(ErpCategory category) {
    Get.toNamed(Routes.CATEGORY_DETAIL, arguments: category);
  }

  void showAddCategoryDialog() {
    Get.toNamed(Routes.CATEGORY_DETAIL);
  }

  Future<void> refreshCategories() async {
    await _loadCategories();
  }
}

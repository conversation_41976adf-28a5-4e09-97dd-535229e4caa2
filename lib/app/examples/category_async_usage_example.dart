import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';
import '../models/erp_category.dart';
import '../repositories/category_repository.dart';
import '../providers/box_provider.dart';

/// 這個文件展示了如何在實際應用中使用 CategoryRepository 的異步方法
/// 
/// 異步方法的優勢：
/// 1. 不會阻塞 UI 線程，提供更好的用戶體驗
/// 2. 適合處理大量數據操作
/// 3. 支持錯誤處理和日誌記錄
/// 4. 可以與 GetX 的響應式狀態管理完美配合

class CategoryAsyncUsageExample extends GetxController {
  late final CategoryRepository _categoryRepository;
  late final Talker _talker;

  // 響應式變量
  final categories = <ErpCategory>[].obs;
  final isLoading = false.obs;
  final errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeRepository();
  }

  void _initializeRepository() {
    final boxProvider = Get.find<BoxProvider>();
    _talker = boxProvider.talker;
    _categoryRepository = CategoryRepository(boxProvider.store, talker: _talker);
  }

  /// 異步加載所有類別
  Future<void> loadCategories() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final categoryList = await _categoryRepository.getAllSortedAsync();
      categories.value = categoryList;

      _talker.info('Successfully loaded ${categoryList.length} categories');
    } catch (e, s) {
      errorMessage.value = 'Failed to load categories: $e';
      _talker.error('Failed to load categories', e, s);
    } finally {
      isLoading.value = false;
    }
  }

  /// 異步搜索類別
  Future<void> searchCategories(String searchTerm) async {
    if (searchTerm.isEmpty) {
      await loadCategories();
      return;
    }

    try {
      isLoading.value = true;
      errorMessage.value = '';

      final searchResults = await _categoryRepository.searchByNameAsync(searchTerm);
      categories.value = searchResults;

      _talker.info('Found ${searchResults.length} categories matching "$searchTerm"');
    } catch (e, s) {
      errorMessage.value = 'Failed to search categories: $e';
      _talker.error('Failed to search categories', e, s);
    } finally {
      isLoading.value = false;
    }
  }

  /// 異步添加新類別
  Future<bool> addCategory({
    required String name,
    required String color,
    int? sort,
  }) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final category = ErpCategory(
        name: name,
        color: color,
        sort: sort ?? await _getNextSortOrder(),
      );

      final id = await _categoryRepository.addAsync(category);
      
      if (id > 0) {
        _talker.info('Successfully added category: $name');
        await loadCategories(); // 重新加載列表
        return true;
      } else {
        throw Exception('Failed to add category');
      }
    } catch (e, s) {
      errorMessage.value = 'Failed to add category: $e';
      _talker.error('Failed to add category', e, s);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 異步更新類別
  Future<bool> updateCategory(ErpCategory category) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      await _categoryRepository.updateAsync(category);
      
      _talker.info('Successfully updated category: ${category.name}');
      await loadCategories(); // 重新加載列表
      return true;
    } catch (e, s) {
      errorMessage.value = 'Failed to update category: $e';
      _talker.error('Failed to update category', e, s);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 異步軟刪除類別
  Future<bool> deleteCategory(int categoryId) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final success = await _categoryRepository.softDeleteAsync(categoryId);
      
      if (success) {
        _talker.info('Successfully deleted category with ID: $categoryId');
        await loadCategories(); // 重新加載列表
        return true;
      } else {
        throw Exception('Category not found');
      }
    } catch (e, s) {
      errorMessage.value = 'Failed to delete category: $e';
      _talker.error('Failed to delete category', e, s);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 異步恢復已刪除的類別
  Future<bool> restoreCategory(int categoryId) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final success = await _categoryRepository.restoreAsync(categoryId);
      
      if (success) {
        _talker.info('Successfully restored category with ID: $categoryId');
        await loadCategories(); // 重新加載列表
        return true;
      } else {
        throw Exception('Category not found or not deleted');
      }
    } catch (e, s) {
      errorMessage.value = 'Failed to restore category: $e';
      _talker.error('Failed to restore category', e, s);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 異步批量操作示例
  Future<bool> batchCreateCategories(List<Map<String, dynamic>> categoryData) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final categories = categoryData.map((data) => ErpCategory(
        name: data['name'],
        color: data['color'],
        sort: data['sort'],
      )).toList();

      final ids = await _categoryRepository.putManyAsync(categories);
      
      _talker.info('Successfully created ${ids.length} categories');
      await loadCategories(); // 重新加載列表
      return true;
    } catch (e, s) {
      errorMessage.value = 'Failed to create categories: $e';
      _talker.error('Failed to create categories', e, s);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 獲取統計信息
  Future<Map<String, int>> getCategoryStats() async {
    try {
      final totalCount = await _categoryRepository.countAsync(includeDeleted: true);
      final activeCount = await _categoryRepository.countAsync(includeDeleted: false);
      final deletedCount = totalCount - activeCount;

      return {
        'total': totalCount,
        'active': activeCount,
        'deleted': deletedCount,
      };
    } catch (e, s) {
      _talker.error('Failed to get category stats', e, s);
      return {
        'total': 0,
        'active': 0,
        'deleted': 0,
      };
    }
  }

  /// 獲取下一個排序順序
  Future<int> _getNextSortOrder() async {
    try {
      final categories = await _categoryRepository.getAllSortedAsync(ascending: false);
      if (categories.isEmpty) {
        return 1;
      }
      return (categories.first.sort ?? 0) + 1;
    } catch (e) {
      return 1;
    }
  }

  /// 清理資源
  @override
  void onClose() {
    super.onClose();
  }
}

/// 使用示例：
/// 
/// ```dart
/// class CategoryListPage extends StatelessWidget {
///   final CategoryAsyncUsageExample controller = Get.put(CategoryAsyncUsageExample());
/// 
///   @override
///   Widget build(BuildContext context) {
///     return Scaffold(
///       appBar: AppBar(title: Text('Categories')),
///       body: Obx(() {
///         if (controller.isLoading.value) {
///           return Center(child: CircularProgressIndicator());
///         }
/// 
///         if (controller.errorMessage.value.isNotEmpty) {
///           return Center(child: Text(controller.errorMessage.value));
///         }
/// 
///         return ListView.builder(
///           itemCount: controller.categories.length,
///           itemBuilder: (context, index) {
///             final category = controller.categories[index];
///             return ListTile(
///               title: Text(category.name ?? ''),
///               subtitle: Text('Sort: ${category.sort}'),
///               trailing: IconButton(
///                 icon: Icon(Icons.delete),
///                 onPressed: () => controller.deleteCategory(category.id!),
///               ),
///             );
///           },
///         );
///       }),
///       floatingActionButton: FloatingActionButton(
///         onPressed: () => controller.addCategory(
///           name: 'New Category',
///           color: '#FF0000',
///         ),
///         child: Icon(Icons.add),
///       ),
///     );
///   }
/// }
/// ```

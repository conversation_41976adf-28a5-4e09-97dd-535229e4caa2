import 'package:flutter_test/flutter_test.dart';
import 'package:pocket_trac/app/models/erp_category.dart';
import 'package:pocket_trac/app/repositories/category_repository.dart';
import 'package:pocket_trac/objectbox.g.dart';
import 'package:talker_flutter/talker_flutter.dart';

void main() {
  group('CategoryRepository Async Tests', () {
    late Store store;
    late CategoryRepository repository;
    late Talker talker;

    setUp(() async {
      // 創建內存數據庫用於測試
      store = Store(getObjectBoxModel(), directory: null);
      talker = TalkerFlutter.init();
      repository = CategoryRepository(store, talker: talker);
    });

    tearDown(() {
      store.close();
    });

    test('addAsync should create a new category', () async {
      // Arrange
      final category = ErpCategory(
        name: '測試類別',
        color: '#FF0000',
        sort: 1,
      );

      // Act
      final id = await repository.addAsync(category);

      // Assert
      expect(id, greaterThan(0));
      expect(category.id, equals(id));
      expect(category.createdAt, isNotNull);
      expect(category.updatedAt, isNotNull);
      expect(category.uuid, isNotNull);
    });

    test('getAllAsync should return all categories', () async {
      // Arrange
      final categories = [
        ErpCategory(name: '類別1', color: '#FF0000', sort: 1),
        ErpCategory(name: '類別2', color: '#00FF00', sort: 2),
      ];
      await repository.putManyAsync(categories);

      // Act
      final result = await repository.getAllAsync();

      // Assert
      expect(result.length, equals(2));
      expect(result.map((c) => c.name), containsAll(['類別1', '類別2']));
    });

    test('getByIdAsync should return correct category', () async {
      // Arrange
      final category = ErpCategory(name: '測試類別', color: '#FF0000', sort: 1);
      final id = await repository.addAsync(category);

      // Act
      final result = await repository.getByIdAsync(id);

      // Assert
      expect(result, isNotNull);
      expect(result!.name, equals('測試類別'));
      expect(result.id, equals(id));
    });

    test('getByUuidAsync should return correct category', () async {
      // Arrange
      final category = ErpCategory(name: '測試類別', color: '#FF0000', sort: 1);
      await repository.addAsync(category);

      // Act
      final result = await repository.getByUuidAsync(category.uuid!);

      // Assert
      expect(result, isNotNull);
      expect(result!.name, equals('測試類別'));
      expect(result.uuid, equals(category.uuid));
    });

    test('searchByNameAsync should return matching categories', () async {
      // Arrange
      final categories = [
        ErpCategory(name: '餐飲費用', color: '#FF0000', sort: 1),
        ErpCategory(name: '交通費用', color: '#00FF00', sort: 2),
        ErpCategory(name: '娛樂費用', color: '#0000FF', sort: 3),
      ];
      await repository.putManyAsync(categories);

      // Act
      final result = await repository.searchByNameAsync('費用');

      // Assert
      expect(result.length, equals(3));
      expect(result.every((c) => c.name!.contains('費用')), isTrue);
    });

    test('updateAsync should modify existing category', () async {
      // Arrange
      final category = ErpCategory(name: '原始名稱', color: '#FF0000', sort: 1);
      await repository.addAsync(category);

      // Act
      category.name = '更新名稱';
      await repository.updateAsync(category);

      // Assert
      final updated = await repository.getByIdAsync(category.id!);
      expect(updated!.name, equals('更新名稱'));
      expect(updated.updatedAt, isNotNull);
    });

    test('softDeleteAsync should mark category as deleted', () async {
      // Arrange
      final category = ErpCategory(name: '測試類別', color: '#FF0000', sort: 1);
      final id = await repository.addAsync(category);

      // Act
      final result = await repository.softDeleteAsync(id);

      // Assert
      expect(result, isTrue);
      final deleted = await repository.getByIdAsync(id);
      expect(deleted!.deletedAt, isNotNull);

      // 確認在不包含已刪除項目的查詢中不會出現
      final activeCategories = await repository.getAllAsync(includeDeleted: false);
      expect(activeCategories.any((c) => c.id == id), isFalse);
    });

    test('restoreAsync should restore soft deleted category', () async {
      // Arrange
      final category = ErpCategory(name: '測試類別', color: '#FF0000', sort: 1);
      final id = await repository.addAsync(category);
      await repository.softDeleteAsync(id);

      // Act
      final result = await repository.restoreAsync(id);

      // Assert
      expect(result, isTrue);
      final restored = await repository.getByIdAsync(id);
      expect(restored!.deletedAt, isNull);

      // 確認在活動類別查詢中會出現
      final activeCategories = await repository.getAllAsync(includeDeleted: false);
      expect(activeCategories.any((c) => c.id == id), isTrue);
    });

    test('hardDeleteAsync should permanently remove category', () async {
      // Arrange
      final category = ErpCategory(name: '測試類別', color: '#FF0000', sort: 1);
      final id = await repository.addAsync(category);

      // Act
      final result = await repository.hardDeleteAsync(id);

      // Assert
      expect(result, isTrue);
      final deleted = await repository.getByIdAsync(id);
      expect(deleted, isNull);
    });

    test('getAllSortedAsync should return categories in correct order', () async {
      // Arrange
      final categories = [
        ErpCategory(name: '類別C', color: '#FF0000', sort: 3),
        ErpCategory(name: '類別A', color: '#00FF00', sort: 1),
        ErpCategory(name: '類別B', color: '#0000FF', sort: 2),
      ];
      await repository.putManyAsync(categories);

      // Act
      final ascending = await repository.getAllSortedAsync(ascending: true);
      final descending = await repository.getAllSortedAsync(ascending: false);

      // Assert
      expect(ascending.map((c) => c.name), equals(['類別A', '類別B', '類別C']));
      expect(descending.map((c) => c.name), equals(['類別C', '類別B', '類別A']));
    });

    test('countAsync should return correct count', () async {
      // Arrange
      final categories = [
        ErpCategory(name: '類別1', color: '#FF0000', sort: 1),
        ErpCategory(name: '類別2', color: '#00FF00', sort: 2),
        ErpCategory(name: '類別3', color: '#0000FF', sort: 3),
      ];
      await repository.putManyAsync(categories);

      // 軟刪除一個類別
      await repository.softDeleteAsync(categories[0].id!);

      // Act
      final totalCount = await repository.countAsync(includeDeleted: true);
      final activeCount = await repository.countAsync(includeDeleted: false);

      // Assert
      expect(totalCount, equals(3));
      expect(activeCount, equals(2));
    });

    test('deleteAllAsync should remove all categories', () async {
      // Arrange
      final categories = [
        ErpCategory(name: '類別1', color: '#FF0000', sort: 1),
        ErpCategory(name: '類別2', color: '#00FF00', sort: 2),
      ];
      await repository.putManyAsync(categories);

      // Act
      final deletedCount = await repository.deleteAllAsync();

      // Assert
      expect(deletedCount, equals(2));
      final remaining = await repository.getAllAsync();
      expect(remaining.length, equals(0));
    });
  });
}
